<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>IsCaseEditable__c</fullName>
    <description>Flag tecnico che determina se un Case è modificabile basato sul Record Type e sulla presenza di valori nella Categoria</description>
    <externalId>false</externalId>
    <formula>RecordType.DeveloperName == "ur_CaseCRM" || (RecordType.DeveloperName == "ur_CaseES" &amp;&amp; NOT(ISBLANK(Categoria__c)))</formula>
    <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    <label>IsCaseEditable</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Checkbox</type>
</CustomField>

<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>urcs_CaseEditOggetto</fullName>
    <active>true</active>
    <errorConditionFormula>
AND(
    NOT(ISNEW()),
    ISCHANGED(Subject),
    OR(
        AND(
            RecordType.DeveloperName == "ur_CaseCRM",
            OR(
                ISBLANK(UtAssegnatario__c),
                $User.Id != UtAssegnatario__c,
                IsClosed = true
            )
        ),
        OR(
            RecordType.DeveloperName == "ur_CasePQ",
            RecordType.DeveloperName == "ur_CaseSitoWeb",
            RecordType.DeveloperName == "ur_CaseAR",
            RecordType.DeveloperName == "ur_CaseES"
        )
    )
)</errorConditionFormula>
    <errorDisplayField>Subject</errorDisplayField>
    <errorMessage>Non è possibile modificare il valore di questo campo: Oggetto</errorMessage>
</ValidationRule>
